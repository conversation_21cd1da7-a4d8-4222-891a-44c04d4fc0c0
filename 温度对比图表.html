<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东涌与白云温度对比图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@2.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 40px;
        }
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stats-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .stats-card h3 {
            margin-top: 0;
            color: #333;
        }
        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .dongchong { color: #FF6B6B; }
        .baiyun { color: #4ECDC4; }
        .file-input {
            margin-bottom: 20px;
            padding: 10px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            text-align: center;
        }
        input[type="file"] {
            margin: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>东涌与白云温度对比分析</h1>
        
        <div class="file-input">
            <p>请选择CSV文件（或使用默认数据）：</p>
            <input type="file" id="csvFile" accept=".csv" />
            <button onclick="loadDefaultData()">使用默认数据</button>
        </div>

        <div class="stats-container" id="statsContainer" style="display: none;">
            <div class="stats-card">
                <h3>📊 基本统计</h3>
                <div class="stats-item">
                    <span>数据点总数：</span>
                    <span id="totalPoints">-</span>
                </div>
                <div class="stats-item">
                    <span>时间范围：</span>
                    <span id="timeRange">-</span>
                </div>
                <div class="stats-item">
                    <span>温度相关系数：</span>
                    <span id="correlation">-</span>
                </div>
            </div>
            
            <div class="stats-card">
                <h3 class="dongchong">🌡️ 东涌温度</h3>
                <div class="stats-item">
                    <span>平均温度：</span>
                    <span id="dongchongAvg">-</span>
                </div>
                <div class="stats-item">
                    <span>最高温度：</span>
                    <span id="dongchongMax">-</span>
                </div>
                <div class="stats-item">
                    <span>最低温度：</span>
                    <span id="dongchongMin">-</span>
                </div>
                <div class="stats-item">
                    <span>标准差：</span>
                    <span id="dongchongStd">-</span>
                </div>
            </div>
            
            <div class="stats-card">
                <h3 class="baiyun">🌡️ 白云温度</h3>
                <div class="stats-item">
                    <span>平均温度：</span>
                    <span id="baiyunAvg">-</span>
                </div>
                <div class="stats-item">
                    <span>最高温度：</span>
                    <span id="baiyunMax">-</span>
                </div>
                <div class="stats-item">
                    <span>最低温度：</span>
                    <span id="baiyunMin">-</span>
                </div>
                <div class="stats-item">
                    <span>标准差：</span>
                    <span id="baiyunStd">-</span>
                </div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="temperatureChart"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="differenceChart"></canvas>
        </div>

        <div class="chart-container">
            <canvas id="distributionChart"></canvas>
        </div>
    </div>

    <script>
        let temperatureChart, differenceChart, distributionChart;
        
        // 文件上传处理
        document.getElementById('csvFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const csv = e.target.result;
                    parseAndVisualize(csv);
                };
                reader.readAsText(file, 'UTF-8');
            }
        });

        function loadDefaultData() {
            // 这里可以加载默认的CSV数据
            fetch('东涌和白云温度对比.csv')
                .then(response => response.text())
                .then(csv => parseAndVisualize(csv))
                .catch(error => {
                    console.error('无法加载默认数据:', error);
                    alert('无法加载默认数据，请手动选择CSV文件');
                });
        }

        function parseCSV(csv) {
            const lines = csv.trim().split('\n');
            const data = [];
            
            for (let i = 1; i < lines.length; i++) { // 跳过标题行
                const values = lines[i].split(',');
                if (values.length >= 3) {
                    const time = new Date(values[0]);
                    const dongchong = parseFloat(values[1]);
                    const baiyun = parseFloat(values[2]);
                    
                    if (!isNaN(time.getTime()) && !isNaN(dongchong) && !isNaN(baiyun)) {
                        data.push({
                            time: time,
                            dongchong: dongchong,
                            baiyun: baiyun
                        });
                    }
                }
            }
            
            return data.sort((a, b) => a.time - b.time);
        }

        function calculateStats(data) {
            const dongchongTemps = data.map(d => d.dongchong);
            const baiyunTemps = data.map(d => d.baiyun);
            
            const stats = {
                totalPoints: data.length,
                timeRange: `${data[0].time.toLocaleString()} - ${data[data.length-1].time.toLocaleString()}`,
                dongchong: {
                    avg: average(dongchongTemps),
                    max: Math.max(...dongchongTemps),
                    min: Math.min(...dongchongTemps),
                    std: standardDeviation(dongchongTemps)
                },
                baiyun: {
                    avg: average(baiyunTemps),
                    max: Math.max(...baiyunTemps),
                    min: Math.min(...baiyunTemps),
                    std: standardDeviation(baiyunTemps)
                },
                correlation: correlation(dongchongTemps, baiyunTemps)
            };
            
            return stats;
        }

        function average(arr) {
            return arr.reduce((a, b) => a + b, 0) / arr.length;
        }

        function standardDeviation(arr) {
            const avg = average(arr);
            const squareDiffs = arr.map(value => Math.pow(value - avg, 2));
            return Math.sqrt(average(squareDiffs));
        }

        function correlation(x, y) {
            const n = x.length;
            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
            const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);
            
            const numerator = n * sumXY - sumX * sumY;
            const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
            
            return denominator === 0 ? 0 : numerator / denominator;
        }

        function updateStats(stats) {
            document.getElementById('totalPoints').textContent = stats.totalPoints;
            document.getElementById('timeRange').textContent = stats.timeRange;
            document.getElementById('correlation').textContent = stats.correlation.toFixed(4);
            
            document.getElementById('dongchongAvg').textContent = stats.dongchong.avg.toFixed(2) + '°C';
            document.getElementById('dongchongMax').textContent = stats.dongchong.max.toFixed(2) + '°C';
            document.getElementById('dongchongMin').textContent = stats.dongchong.min.toFixed(2) + '°C';
            document.getElementById('dongchongStd').textContent = stats.dongchong.std.toFixed(2) + '°C';
            
            document.getElementById('baiyunAvg').textContent = stats.baiyun.avg.toFixed(2) + '°C';
            document.getElementById('baiyunMax').textContent = stats.baiyun.max.toFixed(2) + '°C';
            document.getElementById('baiyunMin').textContent = stats.baiyun.min.toFixed(2) + '°C';
            document.getElementById('baiyunStd').textContent = stats.baiyun.std.toFixed(2) + '°C';
            
            document.getElementById('statsContainer').style.display = 'grid';
        }

        function createTemperatureChart(data) {
            const ctx = document.getElementById('temperatureChart').getContext('2d');
            
            if (temperatureChart) {
                temperatureChart.destroy();
            }
            
            temperatureChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '东涌温度',
                        data: data.map(d => ({x: d.time, y: d.dongchong})),
                        borderColor: '#FF6B6B',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        fill: true,
                        tension: 0.1
                    }, {
                        label: '白云温度',
                        data: data.map(d => ({x: d.time, y: d.baiyun})),
                        borderColor: '#4ECDC4',
                        backgroundColor: 'rgba(78, 205, 196, 0.1)',
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '温度时间序列对比图'
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                displayFormats: {
                                    hour: 'MM-dd HH:mm'
                                }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '温度 (°C)'
                            }
                        }
                    }
                }
            });
        }

        function createDifferenceChart(data) {
            const ctx = document.getElementById('differenceChart').getContext('2d');
            
            if (differenceChart) {
                differenceChart.destroy();
            }
            
            const differences = data.map(d => ({
                x: d.time,
                y: d.dongchong - d.baiyun
            }));
            
            differenceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    datasets: [{
                        label: '温度差异 (东涌 - 白云)',
                        data: differences,
                        backgroundColor: differences.map(d => d.y > 0 ? 'rgba(255, 107, 107, 0.7)' : 'rgba(78, 205, 196, 0.7)'),
                        borderColor: differences.map(d => d.y > 0 ? '#FF6B6B' : '#4ECDC4'),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '温度差异图 (东涌 - 白云)'
                        }
                    },
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                displayFormats: {
                                    hour: 'MM-dd HH:mm'
                                }
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '温度差异 (°C)'
                            }
                        }
                    }
                }
            });
        }

        function createDistributionChart(data) {
            const ctx = document.getElementById('distributionChart').getContext('2d');
            
            if (distributionChart) {
                distributionChart.destroy();
            }
            
            // 创建温度分布直方图数据
            const dongchongTemps = data.map(d => d.dongchong);
            const baiyunTemps = data.map(d => d.baiyun);
            
            const minTemp = Math.min(...dongchongTemps, ...baiyunTemps);
            const maxTemp = Math.max(...dongchongTemps, ...baiyunTemps);
            const binCount = 20;
            const binSize = (maxTemp - minTemp) / binCount;
            
            const dongchongBins = new Array(binCount).fill(0);
            const baiyunBins = new Array(binCount).fill(0);
            const labels = [];
            
            for (let i = 0; i < binCount; i++) {
                labels.push((minTemp + i * binSize).toFixed(1));
            }
            
            dongchongTemps.forEach(temp => {
                const binIndex = Math.min(Math.floor((temp - minTemp) / binSize), binCount - 1);
                dongchongBins[binIndex]++;
            });
            
            baiyunTemps.forEach(temp => {
                const binIndex = Math.min(Math.floor((temp - minTemp) / binSize), binCount - 1);
                baiyunBins[binIndex]++;
            });
            
            distributionChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '东涌温度分布',
                        data: dongchongBins,
                        backgroundColor: 'rgba(255, 107, 107, 0.7)',
                        borderColor: '#FF6B6B',
                        borderWidth: 1
                    }, {
                        label: '白云温度分布',
                        data: baiyunBins,
                        backgroundColor: 'rgba(78, 205, 196, 0.7)',
                        borderColor: '#4ECDC4',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '温度分布直方图'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '温度 (°C)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '频次'
                            }
                        }
                    }
                }
            });
        }

        function parseAndVisualize(csv) {
            try {
                const data = parseCSV(csv);
                
                if (data.length === 0) {
                    alert('无法解析CSV数据，请检查文件格式');
                    return;
                }
                
                const stats = calculateStats(data);
                updateStats(stats);
                
                createTemperatureChart(data);
                createDifferenceChart(data);
                createDistributionChart(data);
                
            } catch (error) {
                console.error('数据处理错误:', error);
                alert('数据处理出错，请检查CSV文件格式');
            }
        }

        // 页面加载时尝试加载默认数据
        window.addEventListener('load', function() {
            loadDefaultData();
        });
    </script>
</body>
</html>
