"""
测试13完整显示三组温度对比折线图
================================

优化版本：
1. 真实时间轴显示
2. 完整的折线图显示
3. 更好的布局和标签
4. 减少数据采样以显示更多细节

输出文件：测试13_完整显示隔热性能对比.png
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from matplotlib import rcParams
from datetime import datetime, timedelta
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False
rcParams['figure.facecolor'] = 'white'
rcParams['axes.facecolor'] = 'white'

def create_complete_comparison():
    """创建完整显示的三个子图温度对比分析"""
    
    # 读取数据
    try:
        df = pd.read_csv('13_fixed_headers_sensor_drop_repaired.csv')
        print(f"成功读取测试13数据，共 {len(df)} 个数据点")
    except Exception as e:
        print(f"读取数据文件时出错: {e}")
        return

    # 获取测试总时长
    total_duration_seconds = df['时间'].max()
    duration_hours = total_duration_seconds / 3600
    print(f"测试总时长: {total_duration_seconds:.0f}秒 ({duration_hours:.1f}小时)")

    # 根据xlsx文件创建时间反推测试开始时间
    file_creation_time = datetime(2025, 7, 7, 17, 26, 0)
    test_start_time = file_creation_time - timedelta(seconds=total_duration_seconds)
    print(f"测试时间: {test_start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {file_creation_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 将相对时间转换为真实时间戳
    df['真实时间'] = df['时间'].apply(lambda x: test_start_time + timedelta(seconds=x))
    
    # 减少采样间隔以显示更多细节
    sample_interval = 10  # 更密集的采样
    sample_df = df.iloc[::sample_interval].copy()
    print(f"数据采样后，使用 {len(sample_df)} 个数据点进行绘图")
    
    # 创建更大的图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(18, 16))
    fig.suptitle('不同遮阳材质的隔热性能对比', fontsize=20, fontweight='bold', y=0.98)
    
    # 第一个子图：遮阳罩性能对比
    ax1.plot(sample_df['真实时间'], sample_df['遮阳罩表面温度'], 
             'b-', linewidth=2.0, label='普通遮阳罩表面温度', alpha=0.9)
    ax1.plot(sample_df['真实时间'], sample_df['遮阳罩背面温度'], 
             'r-', linewidth=2.0, label='普通遮阳罩背面温度', alpha=0.9)
    ax1.plot(sample_df['真实时间'], sample_df['制冷帐篷表面温度'], 
             'g-', linewidth=2.0, label='辐射制冷遮阳罩表面温度', alpha=0.9)
    ax1.plot(sample_df['真实时间'], sample_df['制冷帐篷背面温度'], 
             'orange', linewidth=2.0, label='辐射制冷遮阳罩背面温度', alpha=0.9)
    ax1.plot(sample_df['真实时间'], sample_df['环境温度'], 
             'k-', linewidth=1.5, label='环境温度', alpha=0.8)
    
    ax1.set_ylabel('温度 (°C)', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3, linestyle='-', linewidth=0.8)
    ax1.grid(True, alpha=0.15, linestyle=':', linewidth=0.5, which='minor')
    ax1.legend(loc='upper left', fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax1.set_ylim(25, 70)
    ax1.minorticks_on()
    ax1.set_title('遮阳罩性能对比', fontsize=16, pad=10)
    
    # 第二个子图：皮革材料隔热对比
    ax2.plot(sample_df['真实时间'], sample_df['遮阳罩皮革表面温度'], 
             'b-', linewidth=2.0, label='遮阳罩皮革表面温度', alpha=0.9)
    ax2.plot(sample_df['真实时间'], sample_df['制冷帐篷皮革温度'], 
             'g-', linewidth=2.0, label='制冷帐篷皮革温度', alpha=0.9)
    ax2.plot(sample_df['真实时间'], sample_df['皮革表面温度'], 
             'purple', linewidth=2.0, label='直晒皮革表面温度', alpha=0.9)
    ax2.plot(sample_df['真实时间'], sample_df['皮革背面温度'], 
             'brown', linewidth=2.0, label='直晒皮革背面温度', alpha=0.9)
    ax2.plot(sample_df['真实时间'], sample_df['环境温度'], 
             'k-', linewidth=1.5, label='环境温度', alpha=0.8)
    
    ax2.set_ylabel('温度 (°C)', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3, linestyle='-', linewidth=0.8)
    ax2.grid(True, alpha=0.15, linestyle=':', linewidth=0.5, which='minor')
    ax2.legend(loc='upper left', fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.set_ylim(25, 60)
    ax2.minorticks_on()
    ax2.set_title('皮革材料隔热对比', fontsize=16, pad=10)
    
    # 第三个子图：关键温度对比
    ax3.plot(sample_df['真实时间'], sample_df['遮阳罩表面温度'], 
             'b-', linewidth=2.0, label='最高温度传感器(普通遮阳罩表面)', alpha=0.9)
    ax3.plot(sample_df['真实时间'], sample_df['制冷帐篷皮革温度'], 
             'g-', linewidth=2.0, label='最稳定温度传感器(制冷帐篷皮革)', alpha=0.9)
    ax3.plot(sample_df['真实时间'], sample_df['环境温度'], 
             'k-', linewidth=1.5, label='环境温度', alpha=0.8)
    
    ax3.set_xlabel('时间', fontsize=14, fontweight='bold')
    ax3.set_ylabel('温度 (°C)', fontsize=14, fontweight='bold')
    ax3.grid(True, alpha=0.3, linestyle='-', linewidth=0.8)
    ax3.grid(True, alpha=0.15, linestyle=':', linewidth=0.5, which='minor')
    ax3.legend(loc='upper left', fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax3.set_ylim(25, 70)
    ax3.minorticks_on()
    ax3.set_title('关键温度对比', fontsize=16, pad=10)
    
    # 设置时间轴格式
    for ax in [ax1, ax2, ax3]:
        # 设置时间范围，确保完整显示
        ax.set_xlim(test_start_time, file_creation_time)
        
        # 设置时间格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))     # 每1小时一个主刻度
        ax.xaxis.set_minor_locator(mdates.MinuteLocator(interval=30))  # 每30分钟一个次刻度

        # 旋转时间标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        
        ax.tick_params(axis='both', which='major', labelsize=12)
        ax.tick_params(axis='both', which='minor', labelsize=10)
        
        # 设置坐标轴样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.5)
        ax.spines['bottom'].set_linewidth(1.5)
    
    # 调整布局，确保完整显示
    plt.tight_layout()
    plt.subplots_adjust(top=0.94, bottom=0.12, left=0.08, right=0.96, hspace=0.35)
    
    # 保存图片
    output_filename = '测试13_完整显示隔热性能对比.png'
    plt.savefig(output_filename, 
                dpi=300, 
                bbox_inches='tight',
                facecolor='white',
                edgecolor='none',
                pad_inches=0.3)
    
    print(f"完整显示图表已保存为: {output_filename}")
    
    # 关闭图形以释放内存
    plt.close()
    
    # 输出统计信息
    print("\n=== 完整显示三组对比分析完成 ===")
    print("✓ 横坐标已转换为真实时间显示")
    print("✓ 折线图完整显示，采样密度提高")
    print("✓ 图表尺寸优化，标签清晰可读")
    print("✓ 三组对比：遮阳罩性能、皮革材料、关键温度")

# 主程序执行
if __name__ == "__main__":
    print("开始创建测试13完整显示温度对比图...")
    create_complete_comparison()
    print("\n=== 优化版本分析完成 ===")
