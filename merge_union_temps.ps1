# 合并 in_527_gbk.csv(白云区, GBK) 与 in_526.csv(东涌, UTF-8)，按时间并集升序，仅输出温度
$FileBY = "in_527_gbk.csv"   # 白云区（GBK）
$FileDC = "in_526.csv"       # 东涌（UTF-8）
$OutFile = "合并_白云区_东涌_union_温度.csv"

$encUtf8Bom = New-Object System.Text.UTF8Encoding($true)
$encGbk     = [System.Text.Encoding]::GetEncoding(936)
$encUtf8    = New-Object System.Text.UTF8Encoding($false)

function Load-Temps {
    param(
        [string]$path,
        [System.Text.Encoding]$enc
    )
    $dict = @{}
    $lines = [System.IO.File]::ReadLines($path, $enc)
    foreach ($line in $lines) {
        if ([string]::IsNullOrWhiteSpace($line)) { continue }
        $cells = $line -split ','
        if ($cells.Length -lt 5) { continue }
        $c0 = $cells[0].Trim()
        if ($c0 -like '导出时间*') { continue }
        if ($c0 -eq '序号' -or $c0 -eq '序號') { continue }
        $ts = $cells[1].Trim()
        if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
        $tempStr = $cells[3].Trim()
        $val = $null
        if ($tempStr -ne '') {
            [double]$tmp = 0
            if ([double]::TryParse($tempStr, [ref]$tmp)) { $val = $tmp }
        }
        $dict[$ts] = $val
    }
    return $dict
}

# 加载两站数据
$by = Load-Temps -path $FileBY -enc $encGbk
$dc = Load-Temps -path $FileDC -enc $encUtf8

# 时间并集并升序
$allTimes = @($by.Keys + $dc.Keys | Select-Object -Unique)
$allTimesSorted = $allTimes | Sort-Object { [datetime]::ParseExact($_, 'yyyy-MM-dd HH:mm:ss', $null) }

# 写出
$sw = New-Object System.IO.StreamWriter($OutFile, $false, $encUtf8Bom)
try {
    $sw.WriteLine('时间,白云区_温度(℃),东涌_温度(℃)')
    foreach ($ts in $allTimesSorted) {
        $tBY = if ($by.ContainsKey($ts) -and $by[$ts] -ne $null) { $by[$ts].ToString() } else { '' }
        $tDC = if ($dc.ContainsKey($ts) -and $dc[$ts] -ne $null) { $dc[$ts].ToString() } else { '' }
        $sw.WriteLine("$ts,$tBY,$tDC")
    }
}
finally {
    $sw.Flush(); $sw.Close()
}

Write-Host "合并完成: $OutFile; 总时刻数: $($allTimesSorted.Count)"
