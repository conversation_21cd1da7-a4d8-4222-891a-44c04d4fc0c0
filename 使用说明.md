# 测试11 vs 测试5 折线图对比工具使用说明

## 📋 工具概述

本工具包含多个Python脚本，用于生成测试11和测试5的详细折线图对比分析。

## 📁 文件说明

### 主要脚本文件
1. **`comparison_plotter.py`** - GUI版本对比工具（推荐）
2. **`manual_comparison_script.py`** - 命令行版本对比工具
3. **`run_comparison.py`** - 启动器工具
4. **`simple_line_comparison.py`** - 简化版对比工具

### 数据文件
- **`11_fixed_headers.csv`** - 测试11的温度数据
- **`5_fixed_headers.csv`** - 测试5的温度数据

## 🚀 使用方法

### 方法1: GUI版本（推荐）
```bash
python comparison_plotter.py
```

**功能特点:**
- 图形化界面，操作简单
- 实时数据预览
- 三种对比图表选择
- 自动保存高分辨率图片

### 方法2: 命令行版本
```bash
python manual_comparison_script.py
```

**功能特点:**
- 一键生成所有对比图表
- 自动生成统计报告
- 适合批量处理

### 方法3: 启动器版本
```bash
python run_comparison.py
```

**功能特点:**
- 集成现有GUI程序功能
- 智能依赖检查
- 详细数据预览

## 📊 生成的图表类型

### 1. 完整9参数对比图 (3×3布局)
**文件名**: `complete_comparison.png`
**内容**: 所有9个温度参数的折线图对比
```
遮阳罩表面温度    遮阳罩背面温度    遮阳罩皮革表面温度
制冷帐篷表面温度  制冷帐篷背面温度  制冷帐篷皮革温度
皮革表面温度      皮革背面温度      环境温度
```

### 2. 关键参数对比图 (2×2布局)
**文件名**: `key_comparison.png`
**内容**: 4个最重要参数的详细对比
```
环境温度          遮阳罩表面温度
制冷帐篷表面温度  皮革表面温度
```

### 3. 制冷效果专项对比图 (1×2布局)
**文件名**: `cooling_effect_comparison.png`
**内容**: 制冷系统和皮革温度的专项分析
```
制冷帐篷温度对比  |  皮革温度对比
(表面+背面)      |  (表面+背面)
```

## 🎨 图表设计特点

### 颜色方案
- **测试11**: 红色系 (#FF6B6B) - 表示温度相对较高
- **测试5**: 青色系 (#4ECDC4) - 表示制冷效果好

### 线条样式
- **实线**: 表面温度，线宽2-3px
- **虚线**: 背面温度，线宽2px
- **标记点**: 测试11用圆形，测试5用方形

### 图表元素
- **网格**: 浅色虚线，便于读数
- **图例**: 清晰标注测试编号
- **统计信息**: 显示平均值差异和变化趋势

## 📈 关键对比结果

### 制冷效果对比
```
测试11: 44.1°C → 47.4°C (上升3.3°C)
测试5:  44.8°C → 32.1°C (下降12.7°C)
结论: 测试5制冷效果显著优于测试11
```

### 皮革温度控制
```
测试11: 49.6°C → 57.4°C (上升7.8°C)
测试5:  47.4°C → 32.4°C (下降15.0°C)
结论: 测试5皮革温度控制效果卓越
```

### 测试持续时间
```
测试11: 3.8小时 (1,377个数据点)
测试5:  6.7小时 (2,425个数据点)
结论: 测试5进行了更长时间的验证
```

## 🔧 环境要求

### Python版本
- Python 3.6 或更高版本

### 必需的库
```bash
pip install pandas matplotlib numpy tkinter
```

### 可选的库（用于Excel支持）
```bash
pip install openpyxl xlrd
```

## ❗ 故障排除

### 问题1: 导入错误
**错误**: `ImportError: No module named 'pandas'`
**解决**: 安装缺失的库
```bash
pip install pandas matplotlib numpy
```

### 问题2: 文件未找到
**错误**: `FileNotFoundError: 11_fixed_headers.csv`
**解决**: 确保CSV文件在当前目录中

### 问题3: 中文字体显示问题
**解决**: 脚本会自动尝试多种中文字体
- SimHei (黑体)
- Microsoft YaHei (微软雅黑)
- Arial Unicode MS

### 问题4: 图片保存失败
**解决**: 检查目录写入权限，确保有足够磁盘空间

## 📊 输出文件说明

### 图片文件
- **分辨率**: 300 DPI，适合打印
- **格式**: PNG，支持透明背景
- **尺寸**: 自动优化，保证清晰度

### 统计报告
- **文件名**: `comparison_statistics.txt`
- **编码**: UTF-8
- **内容**: 详细的数值对比和统计分析

## 🎯 使用建议

### 1. 首次使用
1. 确保所有依赖库已安装
2. 检查CSV数据文件完整性
3. 运行GUI版本进行交互式分析

### 2. 批量分析
1. 使用命令行版本自动生成所有图表
2. 查看生成的统计报告
3. 根据需要调整图表参数

### 3. 自定义分析
1. 修改脚本中的颜色方案
2. 调整图表尺寸和布局
3. 添加新的统计指标

## 📞 技术支持

如果遇到问题，请检查：
1. Python环境是否正确配置
2. 所有依赖库是否已安装
3. 数据文件格式是否正确
4. 系统权限是否足够

---

**最后更新**: 2025年1月14日
**版本**: 1.0
**兼容性**: Windows/macOS/Linux
