from __future__ import annotations
import csv
import glob
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Tuple

RowData = Tuple[Optional[str], Optional[float], Optional[float]]

# Find source files by prefix to avoid special chars in names
ROOT = Path('.')
CAND_526 = sorted(glob.glob('30023526_*全部.csv'))
CAND_527 = sorted(glob.glob('30023527_*全部.csv'))
if not CAND_526:
    raise SystemExit('ERR: no CSV for 30023526 found')
if not CAND_527:
    raise SystemExit('ERR: no CSV for 30023527 found')
FILE_526 = Path(CAND_526[0])
FILE_527 = Path(CAND_527[0])

OUT_FILE = Path('合并_白云区_东涌_union_温度.csv')

ENCODINGS = ['utf-8-sig', 'utf-8', 'gbk', 'gb18030']

def read_csv(path: Path) -> Dict[datetime, RowData]:
    last_err: Exception | None = None
    for enc in ENCODINGS:
        try:
            data: Dict[datetime, RowData] = {}
            with path.open('r', encoding=enc, newline='') as f:
                r = csv.reader(f)
                header_seen = False
                for row in r:
                    if not row:
                        continue
                    c0 = row[0].strip() if row[0] is not None else ''
                    # skip meta/header in Chinese or mojibake
                    if c0.startswith('导出时间') or c0 in ('序号', '序號'):
                        header_seen = True
                        continue
                    if not header_seen and len(row) < 5:
                        continue
                    try:
                        ts = datetime.strptime(row[1].strip(), '%Y-%m-%d %H:%M:%S')
                        status = row[2].strip() if len(row) > 2 else ''
                        temp = float(row[3]) if len(row) > 3 and row[3].strip() != '' else None
                        rh = float(row[4]) if len(row) > 4 and row[4].strip() != '' else None
                        data[ts] = (status, temp, rh)
                    except Exception:
                        # ignore non-data rows
                        continue
            # success
            print(f'OK read {path.name} enc={enc} rows={len(data)}')
            return data
        except Exception as e:
            last_err = e
            continue
    raise RuntimeError(f'Cannot read file {path} with common encodings; last_err={last_err}')


def write_union(data_527: Dict[datetime, RowData], data_526: Dict[datetime, RowData], out_path: Path) -> int:
    all_ts = sorted(set(data_527.keys()) | set(data_526.keys()))
    with out_path.open('w', encoding='utf-8-sig', newline='') as f:
        w = csv.writer(f)
        w.writerow(['时间', '白云区_温度(℃)', '东涌_温度(℃)'])
        for ts in all_ts:
            s527 = data_527.get(ts)
            s526 = data_526.get(ts)
            w.writerow([
                ts.strftime('%Y-%m-%d %H:%M:%S'),
                (s527[1] if (s527 and s527[1] is not None) else ''),
                (s526[1] if (s526 and s526[1] is not None) else ''),
            ])
    return len(all_ts)


def main():
    print('Start merge (union, temps only) ...')
    d527 = read_csv(FILE_527)
    d526 = read_csv(FILE_526)
    n = write_union(d527, d526, OUT_FILE)
    print(f'Done. Out={OUT_FILE.name} points={n}')


if __name__ == '__main__':
    main()

