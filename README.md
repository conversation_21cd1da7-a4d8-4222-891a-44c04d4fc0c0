# 数据可视化程序 v2.0

一个功能完整、界面友好的数据可视化程序，支持CSV和Excel文件的读取和多种图表生成。

## 🚀 版本说明

### 当前版本：优化版 v2.0 (推荐)
- **文件名**：`optimized_gui_plotter.py`
- **特点**：全面重构，响应式布局，增强功能
- **推荐使用**：✅ 主要使用版本

### 原始版本：完整版 v1.0 (备份)
- **文件名**：`complete_gui_plotter.py`
- **特点**：原始完整功能版本
- **用途**：作为备份保留

## ✨ 主要功能

### � 数据处理
- **多格式支持**：CSV、Excel (.xlsx/.xls)
- **智能编码检测**：自动识别文件编码
- **数据类型转换**：智能识别数值、日期时间类型

### 📈 图表生成
- **多种图表类型**：线图、散点图、柱状图、面积图
- **颜色主题**：默认、科学、深色、简洁四种主题
- **实时预览**：选择数据后立即更新图表
- **高质量导出**：支持PNG、JPG、PDF、SVG格式

### 🎨 用户界面
- **响应式布局**：根据窗口大小自动调整界面
- **多列网格显示**：数据列选择区域支持2-6列动态布局
- **智能文本处理**：长文本自动截断并提供工具提示

## 🛠️ 安装和使用

### 依赖包
```bash
pip install matplotlib pandas numpy openpyxl
```

### 快速开始
```bash
# 运行优化版程序（推荐）
python optimized_gui_plotter.py

# 或运行原始版程序
python complete_gui_plotter.py
```

### 自动安装依赖
程序会自动检测缺失的依赖包，并提供一键安装功能。

## 📋 使用指南

### 1. 数据加载
1. 点击"浏览"按钮选择数据文件
2. 对于Excel文件，可选择不同工作表
3. 程序会自动处理数据并显示可用列

### 2. 数据映射
1. 选择时间列（可选）
2. 在网格中选择要绘制的数据列
3. 可自定义列标签和颜色
4. 支持全选/清空操作

### 3. 图表配置
1. 设置图表标题和Y轴标签
2. 选择图表类型和颜色主题
3. 调整图表尺寸
4. 实时预览效果

### 4. 导出和保存
1. 生成独立图表窗口
2. 保存为高质量图片文件
3. 全屏预览图表效果
4. 转换Excel为CSV格式

## 🎯 响应式布局

| 窗口宽度 | 布局模式 | 数据列显示 | 特点 |
|---------|---------|-----------|------|
| < 900px | 窄屏模式 | 2列/行 | 紧凑布局 |
| 900-1200px | 中等模式 | 3列/行 | 平衡布局 |
| 1200-1500px | 宽屏模式 | 4列/行 | 宽松布局 |
| > 1500px | 超宽模式 | 6列/行 | 最大空间利用 |

## 📁 项目结构

```
绘图/
├── optimized_gui_plotter.py    # 优化版主程序（推荐使用）
├── complete_gui_plotter.py     # 原始版主程序（备份）
├── README.md                   # 项目说明文档
├── 11.xlsx                     # 示例数据文件
├── 11_fixed_headers.csv        # 示例CSV文件
├── 5.xlsx                      # 示例数据文件
└── 5_fixed_headers.csv         # 示例CSV文件
```

## 🔍 故障排除

### 常见问题
1. **依赖包缺失**：程序会自动提示并提供安装方案
2. **Excel文件读取失败**：建议转换为CSV格式
3. **中文显示异常**：检查系统字体设置
4. **图表显示不完整**：尝试调整窗口大小

### 获取帮助
- 程序内置帮助：点击界面右下角"❓ 帮助"按钮
- 错误信息：程序会显示详细的错误信息和解决建议

---

**推荐使用优化版 v2.0 (`optimized_gui_plotter.py`) 以获得最佳体验！** 🚀

