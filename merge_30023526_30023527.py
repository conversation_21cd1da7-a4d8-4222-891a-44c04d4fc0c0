from __future__ import annotations
import csv
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Tuple

# Input files
FILE_526 = Path("30023526_2025-08-22 14：27：03至2025-08-29 15：27：03全部.csv")
FILE_527 = Path("30023527_2025-08-21 14：25：39至2025-08-29 15：25：39全部.csv")

# Output file
OUT_FILE = Path("合并_白云区(30023527)_东涌(30023526).csv")

ENCODINGS_TO_TRY = ["utf-8-sig", "utf-8", "gbk", "gb18030"]

RowData = Tuple[Optional[str], Optional[float], Optional[float]]  # (status, temp, rh)


def try_open(path: Path):
    last_err = None
    for enc in ENCODINGS_TO_TRY:
        try:
            f = path.open("r", encoding=enc, newline="")
            # quick sniff
            _pos = f.tell()
            f.read(64)
            f.seek(_pos)
            return f, enc
        except Exception as e:
            last_err = e
    raise RuntimeError(f"无法用常见编码读取文件: {path} 错误: {last_err}")


def parse_csv(path: Path) -> Dict[datetime, RowData]:
    data: Dict[datetime, RowData] = {}
    f, enc = try_open(path)
    print(f"读取 {path.name} 使用编码: {enc}")
    with f:
        reader = csv.reader(f)
        header_found = False
        for row in reader:
            if not row:
                continue
            cell0 = row[0].strip()
            # 跳过导出信息行（容错不同编码导致的变体）
            if cell0.startswith("导出时间") or cell0.endswith("导出时间："):
                continue
            # 标题行定位
            if (cell0 in ("序号", "序號")) and (len(row) >= 5):
                header_found = True
                continue
            if not header_found:
                # 如果未显式遇到标题行，但列数满足，也尝试解析（防止编码导致标题识别失败）
                if len(row) < 5:
                    continue
            try:
                # 预期列: 序号,时间,状态,温度(℃),湿度(%RH)
                ts_str = row[1].strip()
                status = row[2].strip() if len(row) > 2 else None
                temp = float(row[3]) if len(row) > 3 and row[3].strip() != '' else None
                rh = float(row[4]) if len(row) > 4 and row[4].strip() != '' else None
                ts = datetime.strptime(ts_str, "%Y-%m-%d %H:%M:%S")
                data[ts] = (status, temp, rh)
            except Exception:
                continue
    return data


def write_merged(
    data_527: Dict[datetime, RowData],
    data_526: Dict[datetime, RowData],
    out_path: Path,
) -> None:
    # 共用同一时间轴：并集升序
    all_ts = sorted(set(data_527.keys()) | set(data_526.keys()))

    out_path.parent.mkdir(parents=True, exist_ok=True)
    with out_path.open("w", encoding="utf-8-sig", newline="") as f:
        writer = csv.writer(f)
        writer.writerow([
            "时间",
            "白云区_状态",
            "白云区_温度(℃)",
            "白云区_湿度(%RH)",
            "东涌_状态",
            "东涌_温度(℃)",
            "东涌_湿度(%RH)",
        ])
        for ts in all_ts:
            s527 = data_527.get(ts)
            s526 = data_526.get(ts)
            row = [
                ts.strftime("%Y-%m-%d %H:%M:%S"),
                (s527[0] if s527 else ""),
                (s527[1] if (s527 and s527[1] is not None) else ""),
                (s527[2] if (s527 and s527[2] is not None) else ""),
                (s526[0] if s526 else ""),
                (s526[1] if (s526 and s526[1] is not None) else ""),
                (s526[2] if (s526 and s526[2] is not None) else ""),
            ]
            writer.writerow(row)


def main():
    try:
        if not FILE_527.exists():
            raise FileNotFoundError(FILE_527)
        if not FILE_526.exists():
            raise FileNotFoundError(FILE_526)

        data_527 = parse_csv(FILE_527)
        data_526 = parse_csv(FILE_526)
        print(f"解析完成: 白云区={len(data_527)}条, 东涌={len(data_526)}条")

        write_merged(data_527, data_526, OUT_FILE)

        line_count = 0
        with OUT_FILE.open('r', encoding='utf-8-sig') as rf:
            for _ in rf:
                line_count += 1

        print(f"合并完成: {OUT_FILE}")
        print(f"时间轴总长度(并集): {line_count - 1}")
    except Exception:
        print("运行失败，详细错误如下：")
        traceback.print_exc()
        raise


if __name__ == "__main__":
    main()

