from __future__ import annotations
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

IN_CSV = Path('merged_union_temps.csv')
OUT_PNG = Path('白云区_东涌_温度并集曲线.png')
OUT_DIFF_PNG = Path('白云区_东涌_温度差曲线(并集).png')

# 读取数据
# merged_union_temps.csv 是 UTF-8 BOM

df = pd.read_csv(IN_CSV, encoding='utf-8-sig')
df['time'] = pd.to_datetime(df['time'])
df = df.sort_values('time')

# 折线图：两地温度随时间
plt.figure(figsize=(16, 6), dpi=150)
plt.plot(df['time'], df['baiyun_temp'], label='白云区', color='#1f77b4', linewidth=1.2)
plt.plot(df['time'], df['dongchong_temp'], label='东涌', color='#ff7f0e', linewidth=1.2)
plt.title('白云区 vs 东涌 温度（并集时间轴）')
plt.xlabel('时间')
plt.ylabel('温度(℃)')
plt.legend()
plt.grid(alpha=0.3)
plt.tight_layout()
plt.savefig(OUT_PNG)
plt.close()

# 温度差：仅对两地同时有值的时间点
mask = df['baiyun_temp'].notna() & df['dongchong_temp'].notna()
df_diff = df.loc[mask, ['time']].copy()
df_diff['diff'] = df.loc[mask, 'baiyun_temp'] - df.loc[mask, 'dongchong_temp']

plt.figure(figsize=(16, 4), dpi=150)
plt.plot(df_diff['time'], df_diff['diff'], color='#2ca02c', linewidth=1.0)
plt.axhline(0, color='gray', linewidth=0.8)
plt.title('白云区 - 东涌 温度差（仅交集时刻）')
plt.xlabel('时间')
plt.ylabel('温度差(℃)')
plt.grid(alpha=0.3)
plt.tight_layout()
plt.savefig(OUT_DIFF_PNG)
plt.close()

print(f'图已生成:\n - {OUT_PNG}\n - {OUT_DIFF_PNG}')

