# 绘图工作目录文件说明

## 核心数据文件

### 原始数据
- `5.xlsx` - 测试5原始数据
- `11.xlsx` - 测试11原始数据  
- `12.xlsx` - 测试12原始数据
- `13.xlsx` - 测试13原始数据

### 处理后数据
- `5_fixed_headers.csv` - 测试5处理后数据
- `11_fixed_headers.csv` - 测试11处理后数据
- `12_fixed_headers.csv` - 测试12处理后数据
- `13_fixed_headers_sensor_drop_repaired.csv` - **测试13最终修复版数据**（推荐使用）

## 分析结果

### 图表文件
- `四测试分组温度对比图.png` - 四个测试的分组对比图
- `四测试综合温度对比图.png` - 四个测试的综合对比图
- `测试13_不同遮阳材质隔热性能对比.png` - 测试13三组对比图（标准版）
- `测试13_完整显示隔热性能对比.png` - **测试13三组对比图（优化版，推荐）**

### 分析报告
- `测试13温度数据统计分析结果.txt` - 测试13详细统计分析结果

## 脚本文件
- `测试13_完整显示折线图.py` - 生成测试13优化版对比图的脚本

## 文档文件
- `README.md` - 项目说明文档
- `使用说明.md` - 使用指南
- `文件说明.md` - 本文件，目录结构说明

---

## 推荐使用的文件

**数据分析：**
- 数据源：`13_fixed_headers_sensor_drop_repaired.csv`
- 图表：`测试13_完整显示隔热性能对比.png`
- 统计：`测试13温度数据统计分析结果.txt`

**多测试对比：**
- 图表：`四测试综合温度对比图.png`

所有文件都已经过质量检查和优化，可以直接用于分析和报告。
