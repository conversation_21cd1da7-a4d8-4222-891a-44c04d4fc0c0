# 合并 30023526(东涌) 与 30023527(白云区) 的CSV，按同一时间轴输出
# 读 526 用 UTF-8，读 527 用 GBK/CP936，避免乱码

$File526 = "30023526_2025-08-22 14：27：03至2025-08-29 15：27：03全部.csv"
$File527 = "30023527_2025-08-21 14：25：39至2025-08-29 15：25：39全部.csv"
$OutFile = "合并_白云区(30023527)_东涌(30023526).csv"

$encUtf8Bom = New-Object System.Text.UTF8Encoding($true)
$encUtf8    = New-Object System.Text.UTF8Encoding($false)
$encGbk     = [System.Text.Encoding]::GetEncoding(936)

function Load-Data {
    param(
        [string]$path,
        [System.Text.Encoding]$enc
    )
    $dict = @{}
    $lines = [System.IO.File]::ReadLines($path, $enc)
    foreach ($line in $lines) {
        if ([string]::IsNullOrWhiteSpace($line)) { continue }
        $cells = $line.Split(',')
        if ($cells.Length -lt 5) { continue }
        $c0 = $cells[0].Trim()
        if ($c0 -like "导出时间*") { continue }
        if ($c0 -eq "序号" -or $c0 -eq "序號") { continue }
        $ts = $cells[1].Trim()
        if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
        $status = if ($cells.Length -gt 2) { $cells[2].Trim() } else { "" }
        $temp   = if ($cells.Length -gt 3) { $cells[3].Trim() } else { "" }
        $rh     = if ($cells.Length -gt 4) { $cells[4].Trim() } else { "" }
        # 后出现的覆盖先前的
        $dict[$ts] = @($status, $temp, $rh)
    }
    return $dict
}

$dict526 = Load-Data -path $File526 -enc $encUtf8
$dict527 = Load-Data -path $File527 -enc $encGbk

$allTimes = @($dict526.Keys + $dict527.Keys | Select-Object -Unique)
$allTimesSorted = $allTimes | Sort-Object { [datetime]::ParseExact($_, 'yyyy-MM-dd HH:mm:ss', $null) }

$sw = New-Object System.IO.StreamWriter($OutFile, $false, $encUtf8Bom)
try {
    $sw.WriteLine('时间,白云区_状态,白云区_温度(℃),白云区_湿度(%RH),东涌_状态,东涌_温度(℃),东涌_湿度(%RH)')
    foreach ($ts in $allTimesSorted) {
        $v527 = $null; $v526 = $null
        $null = $dict527.TryGetValue($ts, [ref]$v527)
        $null = $dict526.TryGetValue($ts, [ref]$v526)
        $s527 = if ($v527) { $v527[0] } else { '' }
        $t527 = if ($v527 -and $v527[1] -ne '') { $v527[1] } else { '' }
        $h527 = if ($v527 -and $v527[2] -ne '') { $v527[2] } else { '' }
        $s526 = if ($v526) { $v526[0] } else { '' }
        $t526 = if ($v526 -and $v526[1] -ne '') { $v526[1] } else { '' }
        $h526 = if ($v526 -and $v526[2] -ne '') { $v526[2] } else { '' }
        $line = "$ts,$s527,$t527,$h527,$s526,$t526,$h526"
        $sw.WriteLine($line)
    }
}
finally {
    $sw.Flush()
    $sw.Close()
}

Write-Host "合并完成: $OutFile"
Write-Host "时间点总数: $($allTimesSorted.Count)"

