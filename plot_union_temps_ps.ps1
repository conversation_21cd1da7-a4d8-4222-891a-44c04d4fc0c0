# 使用 .NET 图表生成温度曲线与温差曲线（无需安装第三方库）
# 输入：merged_union_temps.csv（UTF-8 BOM），列：time,baiyun_temp,dongchong_temp
# 输出：
#  - temp_union.png
#  - temp_diff_intersection.png

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Windows.Forms.DataVisualization

$InCsv = 'merged_union_temps.csv'
$Out1  = 'temp_union.png'
$Out2  = 'temp_diff_intersection.png'

# 读取数据
$encUtf8B = New-Object System.Text.UTF8Encoding($true)
$lines = [System.IO.File]::ReadAllLines($InCsv, $encUtf8B)
if ($lines.Length -lt 2) { throw 'CSV empty' }

# 解析
$rows = @()
for ($i=1; $i -lt $lines.Length; $i++) {
    $line = $lines[$i]
    if ([string]::IsNullOrWhiteSpace($line)) { continue }
    $cells = $line -split ','
    if ($cells.Length -lt 3) { continue }
    $ts = $cells[0].Trim()
    try { $time = [datetime]$ts } catch { continue }
    $by  = $cells[1].Trim()
    $dc  = $cells[2].Trim()
    [double]$byv = 0; $byOk = [double]::TryParse($by, [ref]$byv)
    [double]$dcv = 0; $dcOk = [double]::TryParse($dc, [ref]$dcv)
    $rows += [pscustomobject]@{ time=$time; BY = $(if($byOk){$byv}else{$null}); DC = $(if($dcOk){$dcv}else{$null}) }
}

if ($rows.Count -eq 0) { throw 'no data' }

# ===== 图1：两地温度并集曲线 =====
$chart1 = New-Object System.Windows.Forms.DataVisualization.Charting.Chart
$chart1.Width  = 1600
$chart1.Height = 600
$ca1 = New-Object System.Windows.Forms.DataVisualization.Charting.ChartArea 'CA1'
$ca1.AxisX.LabelStyle.Format = 'yyyy-MM-dd HH:mm'
$ca1.AxisX.IntervalAutoMode = [System.Windows.Forms.DataVisualization.Charting.IntervalAutoMode]::VariableCount
$ca1.AxisX.MajorGrid.Enabled = $true
$ca1.AxisX.MajorGrid.LineColor = 'Gainsboro'
$ca1.AxisY.Title = 'Temp (°C)'
$ca1.AxisY.MajorGrid.Enabled = $true
$ca1.AxisY.MajorGrid.LineColor = 'Gainsboro'
$chart1.ChartAreas.Add($ca1) | Out-Null

$serBY = New-Object System.Windows.Forms.DataVisualization.Charting.Series 'Baiyun'
$serBY.ChartType = [System.Windows.Forms.DataVisualization.Charting.SeriesChartType]::FastLine
$serBY.XValueType = [System.Windows.Forms.DataVisualization.Charting.ChartValueType]::DateTime
$serBY.Color = [System.Drawing.Color]::FromArgb(31,119,180)

$serDC = New-Object System.Windows.Forms.DataVisualization.Charting.Series 'Dongchong'
$serDC.ChartType = [System.Windows.Forms.DataVisualization.Charting.SeriesChartType]::FastLine
$serDC.XValueType = [System.Windows.Forms.DataVisualization.Charting.ChartValueType]::DateTime
$serDC.Color = [System.Drawing.Color]::FromArgb(255,127,14)

foreach ($r in $rows) {
    if ($null -ne $r.BY) { [void]$serBY.Points.AddXY($r.time, $r.BY) }
    if ($null -ne $r.DC) { [void]$serDC.Points.AddXY($r.time, $r.DC) }
}
$chart1.Series.Add($serBY) | Out-Null
$chart1.Series.Add($serDC) | Out-Null

$lgd1 = New-Object System.Windows.Forms.DataVisualization.Charting.Legend
$lgd1.Docking = 'Top'
$chart1.Legends.Add($lgd1) | Out-Null

$chart1.Titles.Add('Baiyun vs Dongchong (Union Time Axis)') | Out-Null
$chart1.SaveImage($Out1, 'Png')

# ===== 图2：温度差（仅交集时刻） =====
$chart2 = New-Object System.Windows.Forms.DataVisualization.Charting.Chart
$chart2.Width  = 1600
$chart2.Height = 400
$ca2 = New-Object System.Windows.Forms.DataVisualization.Charting.ChartArea 'CA2'
$ca2.AxisX.LabelStyle.Format = 'yyyy-MM-dd HH:mm'
$ca2.AxisX.MajorGrid.Enabled = $true
$ca2.AxisX.MajorGrid.LineColor = 'Gainsboro'
$ca2.AxisY.Title = 'Temp Diff (°C)'
$ca2.AxisY.MajorGrid.Enabled = $true
$ca2.AxisY.MajorGrid.LineColor = 'Gainsboro'
$chart2.ChartAreas.Add($ca2) | Out-Null

$serDiff = New-Object System.Windows.Forms.DataVisualization.Charting.Series 'Baiyun-Dongchong'
$serDiff.ChartType = [System.Windows.Forms.DataVisualization.Charting.SeriesChartType]::FastLine
$serDiff.XValueType = [System.Windows.Forms.DataVisualization.Charting.ChartValueType]::DateTime
$serDiff.Color = [System.Drawing.Color]::FromArgb(44,160,44)

foreach ($r in $rows) {
    if (($null -ne $r.BY) -and ($null -ne $r.DC)) {
        [void]$serDiff.Points.AddXY($r.time, ($r.BY - $r.DC))
    }
}
$chart2.Series.Add($serDiff) | Out-Null
$chart2.Titles.Add('Baiyun - Dongchong Temp Diff (Intersection Only)') | Out-Null
$chart2.SaveImage($Out2, 'Png')

Write-Host "图已生成:`n - $Out1`n - $Out2"

