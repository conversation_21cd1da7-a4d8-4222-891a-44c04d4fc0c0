# 简版：按时间并集升序合并，仅输出温度（白云区、东涌）。
# 依赖文件：in_527_gbk.csv（GBK），in_526.csv（UTF-8）

$FileBY = 'in_527_gbk.csv'   # 白云区（GBK）
$FileDC = 'in_526.csv'       # 东涌（UTF-8）
$OutFile = '合并_白云区_东涌_union_温度.csv'

$encGbk   = [System.Text.Encoding]::GetEncoding(936)
$encUtf8  = New-Object System.Text.UTF8Encoding($false)
$encUtf8B = New-Object System.Text.UTF8Encoding($true)

# 读入全部行
$linesBY = [System.IO.File]::ReadAllLines($FileBY, $encGbk)
$linesDC = [System.IO.File]::ReadAllLines($FileDC, $encUtf8)

# 解析到哈希表（时间 -> 温度字符串）
$by = @{}
foreach ($line in $linesBY) {
    if ([string]::IsNullOrWhiteSpace($line)) { continue }
    $cells = $line -split ','
    if ($cells.Length -lt 5) { continue }
    $c0 = $cells[0].Trim()
    if ($c0 -like '导出时间*' -or $c0 -eq '序号' -or $c0 -eq '序號') { continue }
    $ts = $cells[1].Trim()
    if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
    $temp = $cells[3].Trim()
    $by[$ts] = $temp
}

$dc = @{}
foreach ($line in $linesDC) {
    if ([string]::IsNullOrWhiteSpace($line)) { continue }
    $cells = $line -split ','
    if ($cells.Length -lt 5) { continue }
    $c0 = $cells[0].Trim()
    if ($c0 -like '导出时间*' -or $c0 -eq '序号' -or $c0 -eq '序號') { continue }
    $ts = $cells[1].Trim()
    if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
    $temp = $cells[3].Trim()
    $dc[$ts] = $temp
}

# 并集时间并升序
$allTimes = @($by.Keys + $dc.Keys | Select-Object -Unique)
$sortedTimes = $allTimes | Sort-Object { [datetime]::ParseExact($_, 'yyyy-MM-dd HH:mm:ss', $null) }

# 生成输出
$result = New-Object System.Collections.Generic.List[string]
$result.Add('时间,白云区_温度(℃),东涌_温度(℃)') | Out-Null
foreach ($ts in $sortedTimes) {
    $tBY = if ($by.ContainsKey($ts) -and $by[$ts]) { $by[$ts] } else { '' }
    $tDC = if ($dc.ContainsKey($ts) -and $dc[$ts]) { $dc[$ts] } else { '' }
    $result.Add("$ts,$tBY,$tDC") | Out-Null
}

[System.IO.File]::WriteAllLines($OutFile, $result, $encUtf8B)

Write-Host "合并完成: $OutFile  时间点数: $($sortedTimes.Count)"

