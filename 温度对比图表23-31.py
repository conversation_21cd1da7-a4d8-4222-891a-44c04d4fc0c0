#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东涌和白云温度对比图表生成器 (8月23-31日数据)
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_process_data(filename):
    """加载和处理CSV数据"""
    try:
        # 读取CSV文件，处理编码问题
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
        df = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(filename, encoding=encoding, skiprows=1)  # 跳过第一行标题
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            raise ValueError("无法使用任何编码读取文件")
        
        # 设置正确的列名 - 修正列顺序
        df.columns = ['序号', '时间', '白云温度', '东涌温度']
        
        # 转换时间列为datetime格式
        df['时间'] = pd.to_datetime(df['时间'])
        
        # 确保温度数据为数值类型 - 修正列顺序
        df['白云温度'] = pd.to_numeric(df['白云温度'], errors='coerce')
        df['东涌温度'] = pd.to_numeric(df['东涌温度'], errors='coerce')
        
        # 删除包含NaN的行
        df = df.dropna()

        # 过滤掉8月31日的数据，只保留8月23-30日
        df = df[df['时间'].dt.date < pd.to_datetime('2025-08-31').date()]

        return df
    except Exception as e:
        print(f"数据加载错误: {e}")
        return None

def create_ultra_wide_timeline_chart(df):
    """创建超超宽时间轴图表 - 专为8天数据设计"""
    # 创建超超宽图表 - 更宽以适应更多数据
    fig, ax = plt.subplots(1, 1, figsize=(48, 10))
    
    # 绘制温度曲线 - 修正颜色对应
    ax.plot(df['时间'], df['东涌温度'], label='东涌温度', color='#FF6B6B', linewidth=2, alpha=0.9)
    ax.plot(df['时间'], df['白云温度'], label='白云温度', color='#4ECDC4', linewidth=2, alpha=0.9)

    # 添加填充区域
    ax.fill_between(df['时间'], df['东涌温度'], alpha=0.1, color='#FF6B6B')
    ax.fill_between(df['时间'], df['白云温度'], alpha=0.1, color='#4ECDC4')
    
    ax.set_title('东涌与白云温度对比 - 详细时间轴 (8月23-30日)', fontsize=24, fontweight='bold', pad=30)
    ax.set_xlabel('时间', fontsize=16)
    ax.set_ylabel('温度 (°C)', fontsize=16)
    ax.legend(fontsize=16, loc='upper right')
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.grid(True, alpha=0.2, linestyle=':', linewidth=0.3, which='minor')
    
    # 设置详细的时间轴 - 适应8天数据，从8月23日0点开始
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d\n%H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))  # 每6小时一个主标签
    ax.xaxis.set_minor_locator(mdates.HourLocator(interval=2))  # 每2小时一个小标签

    # 设置x轴范围从8月23日0点开始到8月31日0点
    from datetime import datetime
    start_time = datetime(2025, 8, 23, 0, 0, 0)
    end_time = datetime(2025, 8, 31, 0, 0, 0)
    ax.set_xlim(start_time, end_time)

    # 设置标签样式
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=0, fontsize=10, ha='center')
    
    # 添加温度范围标注 - 确保颜色对应正确
    temp_range_dc = f"{df['东涌温度'].min():.1f}°C - {df['东涌温度'].max():.1f}°C"
    temp_range_by = f"{df['白云温度'].min():.1f}°C - {df['白云温度'].max():.1f}°C"

    ax.text(0.02, 0.98, f'东涌温度范围: {temp_range_dc}', transform=ax.transAxes,
            fontsize=14, verticalalignment='top',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='#FF6B6B', alpha=0.2))

    ax.text(0.02, 0.92, f'白云温度范围: {temp_range_by}', transform=ax.transAxes,
            fontsize=14, verticalalignment='top',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='#4ECDC4', alpha=0.2))
    
    # 添加数据点数量信息
    ax.text(0.02, 0.86, f'数据点总数: {len(df)}', transform=ax.transAxes, 
            fontsize=14, verticalalignment='top',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))
    
    # 优化布局 - 确保所有元素都能完整显示
    plt.subplots_adjust(
        left=0.06,      # 左边距
        right=0.97,     # 右边距
        top=0.88,       # 上边距，为标题留出空间
        bottom=0.15     # 下边距，为x轴标签留出空间
    )
    return fig

def create_temperature_comparison_chart(df):
    """创建温度对比图表"""
    # 创建图表 - 增加宽度以拉长横坐标，增加高度以改善布局
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(32, 20))
    
    # 第一个子图：时间序列对比
    ax1.plot(df['时间'], df['东涌温度'], label='东涌温度', color='#FF6B6B', linewidth=2, alpha=0.8)
    ax1.plot(df['时间'], df['白云温度'], label='白云温度', color='#4ECDC4', linewidth=2, alpha=0.8)
    
    ax1.set_title('东涌与白云温度对比 - 时间序列图 (8月23-30日)', fontsize=20, fontweight='bold', pad=30)
    ax1.set_xlabel('时间', fontsize=16)
    ax1.set_ylabel('温度 (°C)', fontsize=16)
    ax1.legend(fontsize=16)
    ax1.grid(True, alpha=0.3)
    
    # 设置x轴时间格式 - 更密集的时间标签，从8月23日0点开始
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=8))  # 每8小时一个标签
    ax1.xaxis.set_minor_locator(mdates.HourLocator(interval=4))  # 每4小时一个小标签

    # 设置x轴范围从8月23日0点开始到8月31日0点
    from datetime import datetime
    start_time = datetime(2025, 8, 23, 0, 0, 0)
    end_time = datetime(2025, 8, 31, 0, 0, 0)
    ax1.set_xlim(start_time, end_time)

    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45, fontsize=12)
    
    # 添加温度范围填充
    ax1.fill_between(df['时间'], df['东涌温度'], alpha=0.15, color='#FF6B6B')
    ax1.fill_between(df['时间'], df['白云温度'], alpha=0.15, color='#4ECDC4')
    
    # 第二个子图：温度差异图 - 修正差异计算顺序
    temp_diff = df['东涌温度'] - df['白云温度']
    colors = ['#FF6B6B' if x > 0 else '#4ECDC4' for x in temp_diff]
    
    ax2.bar(df['时间'], temp_diff, color=colors, alpha=0.7, width=0.01)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax2.set_title('温度差异图 (东涌 - 白云)', fontsize=20, fontweight='bold', pad=30)
    ax2.set_xlabel('时间', fontsize=16)
    ax2.set_ylabel('温度差异 (°C)', fontsize=16)
    ax2.grid(True, alpha=0.3)
    
    # 设置x轴时间格式 - 更密集的时间标签，从8月23日0点开始
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=8))  # 每8小时一个标签
    ax2.xaxis.set_minor_locator(mdates.HourLocator(interval=4))  # 每4小时一个小标签

    # 设置x轴范围从8月23日0点开始到8月31日0点
    ax2.set_xlim(start_time, end_time)

    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45, fontsize=12)
    
    # 优化布局 - 增加子图间距和边距
    plt.subplots_adjust(
        left=0.08,      # 左边距
        right=0.95,     # 右边距
        top=0.93,       # 上边距
        bottom=0.12,    # 下边距
        hspace=0.35     # 子图间垂直间距
    )

    return fig

def create_daily_analysis_chart(df):
    """创建每日分析图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(32, 20))
    
    # 添加日期列用于分组
    df['日期'] = df['时间'].dt.date
    daily_stats = df.groupby('日期').agg({
        '东涌温度': ['mean', 'max', 'min', 'std'],
        '白云温度': ['mean', 'max', 'min', 'std']
    }).round(2)
    
    # 展平多级列名
    daily_stats.columns = ['_'.join(col).strip() for col in daily_stats.columns]
    daily_stats = daily_stats.reset_index()
    
    # 1. 每日平均温度对比
    x_pos = np.arange(len(daily_stats))
    width = 0.35
    
    ax1.bar(x_pos - width/2, daily_stats['东涌温度_mean'], width, 
            label='东涌平均温度', color='#FF6B6B', alpha=0.8)
    ax1.bar(x_pos + width/2, daily_stats['白云温度_mean'], width, 
            label='白云平均温度', color='#4ECDC4', alpha=0.8)
    
    ax1.set_title('每日平均温度对比', fontsize=16, fontweight='bold')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('平均温度 (°C)')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels([str(date) for date in daily_stats['日期']], rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 每日最高温度对比
    ax2.bar(x_pos - width/2, daily_stats['东涌温度_max'], width, 
            label='东涌最高温度', color='#FF6B6B', alpha=0.8)
    ax2.bar(x_pos + width/2, daily_stats['白云温度_max'], width, 
            label='白云最高温度', color='#4ECDC4', alpha=0.8)
    
    ax2.set_title('每日最高温度对比', fontsize=16, fontweight='bold')
    ax2.set_xlabel('日期')
    ax2.set_ylabel('最高温度 (°C)')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels([str(date) for date in daily_stats['日期']], rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 每日最低温度对比
    ax3.bar(x_pos - width/2, daily_stats['东涌温度_min'], width, 
            label='东涌最低温度', color='#FF6B6B', alpha=0.8)
    ax3.bar(x_pos + width/2, daily_stats['白云温度_min'], width, 
            label='白云最低温度', color='#4ECDC4', alpha=0.8)
    
    ax3.set_title('每日最低温度对比', fontsize=16, fontweight='bold')
    ax3.set_xlabel('日期')
    ax3.set_ylabel('最低温度 (°C)')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels([str(date) for date in daily_stats['日期']], rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 每日温度波动对比（标准差）
    ax4.bar(x_pos - width/2, daily_stats['东涌温度_std'], width, 
            label='东涌温度波动', color='#FF6B6B', alpha=0.8)
    ax4.bar(x_pos + width/2, daily_stats['白云温度_std'], width, 
            label='白云温度波动', color='#4ECDC4', alpha=0.8)
    
    ax4.set_title('每日温度波动对比（标准差）', fontsize=16, fontweight='bold')
    ax4.set_xlabel('日期')
    ax4.set_ylabel('温度标准差 (°C)')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels([str(date) for date in daily_stats['日期']], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 优化布局 - 增加子图间距和边距
    plt.subplots_adjust(
        left=0.08,      # 左边距
        right=0.95,     # 右边距
        top=0.93,       # 上边距
        bottom=0.12,    # 下边距
        hspace=0.35,    # 子图间垂直间距
        wspace=0.25     # 子图间水平间距
    )
    return fig

def print_statistics(df):
    """打印统计信息"""
    print("=" * 70)
    print("温度数据统计摘要 (8月23-30日)")
    print("=" * 70)
    
    print(f"数据时间范围: {df['时间'].min()} 到 {df['时间'].max()}")
    print(f"数据点总数: {len(df)}")
    print(f"数据天数: {(df['时间'].max() - df['时间'].min()).days + 1} 天")
    print()
    
    print("东涌温度统计:")
    print(f"  平均温度: {df['东涌温度'].mean():.2f}°C")
    print(f"  最高温度: {df['东涌温度'].max():.2f}°C")
    print(f"  最低温度: {df['东涌温度'].min():.2f}°C")
    print(f"  标准差: {df['东涌温度'].std():.2f}°C")
    print()
    
    print("白云温度统计:")
    print(f"  平均温度: {df['白云温度'].mean():.2f}°C")
    print(f"  最高温度: {df['白云温度'].max():.2f}°C")
    print(f"  最低温度: {df['白云温度'].min():.2f}°C")
    print(f"  标准差: {df['白云温度'].std():.2f}°C")
    print()
    
    temp_diff = df['东涌温度'] - df['白云温度']
    print("温度差异统计 (东涌 - 白云):")
    print(f"  平均差异: {temp_diff.mean():.2f}°C")
    print(f"  最大差异: {temp_diff.max():.2f}°C")
    print(f"  最小差异: {temp_diff.min():.2f}°C")
    print(f"  标准差: {temp_diff.std():.2f}°C")
    print()
    
    correlation = df['东涌温度'].corr(df['白云温度'])
    print(f"两地温度相关系数: {correlation:.4f}")
    print("=" * 70)

def main():
    """主函数"""
    filename = "东涌和白云温度对比23-31.csv"
    
    # 加载数据
    print("正在加载数据...")
    df = load_and_process_data(filename)
    
    if df is None:
        print("数据加载失败，请检查文件路径和格式。")
        return
    
    print(f"成功加载 {len(df)} 条数据记录")
    
    # 打印统计信息
    print_statistics(df)
    
    # 创建超超宽时间轴图表
    print("正在生成超超宽时间轴图表...")
    fig1 = create_ultra_wide_timeline_chart(df)
    fig1.savefig('东涌白云温度超超宽时间轴图23-30.png', dpi=400, bbox_inches='tight', facecolor='white')
    print("超超宽时间轴图表已保存为: 东涌白云温度超超宽时间轴图23-30.png")

    # 创建主要对比图表
    print("正在生成温度对比图表...")
    fig2 = create_temperature_comparison_chart(df)
    fig2.savefig('东涌白云温度对比图23-30.png', dpi=400, bbox_inches='tight', facecolor='white')
    print("温度对比图表已保存为: 东涌白云温度对比图23-30.png")

    # 创建每日分析图表
    print("正在生成每日分析图表...")
    fig3 = create_daily_analysis_chart(df)
    fig3.savefig('东涌白云每日分析图23-30.png', dpi=400, bbox_inches='tight', facecolor='white')
    print("每日分析图表已保存为: 东涌白云每日分析图23-30.png")
    
    # 显示图表
    plt.show()

if __name__ == "__main__":
    main()
