# Merge two CSVs (in_527_gbk.csv GBK; in_526.csv UTF-8) by union time axis; output temps only
$FileBY = 'in_527_gbk.csv'
$FileDC = 'in_526.csv'
$OutFile = 'merged_union_temps.csv'

$encGbk   = [System.Text.Encoding]::GetEncoding(936)
$encUtf8  = New-Object System.Text.UTF8Encoding($false)
$encUtf8B = New-Object System.Text.UTF8Encoding($true)

# Read all lines
$linesBY = [System.IO.File]::ReadAllLines($FileBY, $encGbk)
$linesDC = [System.IO.File]::ReadAllLines($FileDC, $encUtf8)

# Parse to hashtables: time -> temp
$by = @{}
foreach ($line in $linesBY) {
    if ([string]::IsNullOrWhiteSpace($line)) { continue }
    $cells = $line -split ','
    if ($cells.Length -lt 5) { continue }
    $c0 = $cells[0].Trim()
    if ($c0 -like '导出时间*' -or $c0 -eq '序号' -or $c0 -eq '序號') { continue }
    $ts = $cells[1].Trim()
    if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
    $temp = $cells[3].Trim()
    $by[$ts] = $temp
}

$dc = @{}
foreach ($line in $linesDC) {
    if ([string]::IsNullOrWhiteSpace($line)) { continue }
    $cells = $line -split ','
    if ($cells.Length -lt 5) { continue }
    $c0 = $cells[0].Trim()
    if ($c0 -like '导出时间*' -or $c0 -eq '序号' -or $c0 -eq '序號') { continue }
    $ts = $cells[1].Trim()
    if ($ts -notmatch '^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$') { continue }
    $temp = $cells[3].Trim()
    $dc[$ts] = $temp
}

# Union times ascending
$allTimes = @($by.Keys + $dc.Keys | Select-Object -Unique)
$sortedTimes = $allTimes | Sort-Object { [datetime]::ParseExact($_, 'yyyy-MM-dd HH:mm:ss', $null) }

# Build output lines
$result = New-Object System.Collections.Generic.List[string]
$result.Add('time,baiyun_temp,dongchong_temp') | Out-Null
foreach ($ts in $sortedTimes) {
    $tBY = if ($by.ContainsKey($ts) -and $by[$ts]) { $by[$ts] } else { '' }
    $tDC = if ($dc.ContainsKey($ts) -and $dc[$ts]) { $dc[$ts] } else { '' }
    $result.Add("$ts,$tBY,$tDC") | Out-Null
}

[System.IO.File]::WriteAllLines($OutFile, $result, $encUtf8B)

Write-Host "Merged: $OutFile  points: $($sortedTimes.Count)"
